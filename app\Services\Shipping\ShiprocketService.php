<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShiprocketService extends AbstractShippingService
{
    protected $baseUrl = 'https://apiv2.shiprocket.in/v1/external';
    protected $authToken = null;

    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'shiprocket')->first();
        }

        parent::__construct($shippingPartner);
    }

    /**
     * Get authentication token from Shiprocket
     */
    protected function getAuthToken()
    {
        if ($this->authToken) {
            return $this->authToken;
        }

        try {
            Log::info('Shiprocket Auth Attempt', [
                'email' => $this->apiKey,
                'url' => $this->baseUrl . '/auth/login'
            ]);

            $response = Http::post($this->baseUrl . '/auth/login', [
                'email' => $this->apiKey,
                'password' => $this->apiSecret
            ]);

            Log::info('Shiprocket Auth Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['token'])) {
                    $this->authToken = $data['token'];
                    Log::info('Shiprocket Auth Success', ['token_length' => strlen($this->authToken)]);
                    return $this->authToken;
                }
            }

            Log::error('Shiprocket Auth Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Auth Error: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Create or get pickup location in Shiprocket
     *
     * @param array $address
     * @return string|null
     */
    protected function getOrCreatePickupLocation(array $address): ?string
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return null;
        }

        // First, try to get existing pickup locations
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/settings/company/pickup');

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['shipping_address'])) {
                    foreach ($data['data']['shipping_address'] as $location) {
                        // Check if we have a matching location by postal code and name
                        if ($location['pin_code'] == $address['postal_code'] &&
                            $location['pickup_location'] == $address['name']) {
                            return $location['pickup_location'];
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Shiprocket Get Pickup Locations Error: ' . $e->getMessage());
        }

        // If no matching location found, create a new one
        try {
            $pickupData = [
                'pickup_location' => $address['name'],
                'name' => $address['name'],
                'email' => $this->apiKey, // Use the same email as login
                'phone' => $address['phone'],
                'address' => $address['address'],
                'address_2' => '',
                'city' => $address['city'],
                'state' => $address['state'],
                'country' => $address['country'],
                'pin_code' => $address['postal_code']
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/settings/company/addpickup', $pickupData);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['pickup_location'])) {
                    return $data['pickup_location'];
                }
                return $address['name']; // Fallback to the name we provided
            }

            Log::error('Shiprocket Create Pickup Location Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Create Pickup Location Error: ' . $e->getMessage());
        }

        return $address['name']; // Fallback to the name
    }

    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return 150.00; // Fallback rate
        }

        $weight = 0;
        $dimensions = [
            'length' => 0,
            'width' => 0,
            'height' => 0
        ];

        // Calculate total weight and dimensions
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;

            // Add dimensions (simplified)
            $dimensions['length'] += ($product->length ?? 10);
            $dimensions['width'] += ($product->width ?? 10);
            $dimensions['height'] += ($product->height ?? 5);
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/courier/serviceability', [
                'pickup_postcode' => $fromAddress['postal_code'],
                'delivery_postcode' => $toAddress['postal_code'],
                'weight' => $weight,
                'length' => $dimensions['length'],
                'breadth' => $dimensions['width'],
                'height' => $dimensions['height'],
                'cod' => $order->payment_type == 'cash_on_delivery' ? 1 : 0
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['available_courier_companies']) && !empty($data['data']['available_courier_companies'])) {
                    // Return the cheapest rate
                    $rates = collect($data['data']['available_courier_companies'])->pluck('rate')->toArray();
                    return min($rates);
                }
            }
        } catch (\Exception $e) {
            Log::error('Shiprocket Rate Calculation Error: ' . $e->getMessage());
        }

        return 150.00; // Fallback rate
    }

    /**
     * Create a shipping order with Shiprocket
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [
                'success' => false,
                'message' => 'Authentication failed with Shiprocket'
            ];
        }

        // Ensure pickup location exists in Shiprocket
        $pickupLocation = $this->getOrCreatePickupLocation($fromAddress);
        if (!$pickupLocation) {
            return [
                'success' => false,
                'message' => 'Failed to create pickup location in Shiprocket'
            ];
        }

        $orderItems = [];
        $totalWeight = 0;
        $totalLength = 0;
        $totalWidth = 0;
        $totalHeight = 0;

        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight = ($product->weight ?? 0.5) * $orderDetail->quantity;
            $totalWeight += $weight;
            $totalLength += ($product->length ?? 10);
            $totalWidth += ($product->width ?? 10);
            $totalHeight += ($product->height ?? 5);

            $orderItems[] = [
                'name' => $product->getTranslation('name'),
                'sku' => $product->stocks->first()->sku ?? 'SKU-' . $product->id,
                'units' => $orderDetail->quantity,
                'selling_price' => $orderDetail->price / $orderDetail->quantity,
                'discount' => 0,
                'tax' => $orderDetail->tax / $orderDetail->quantity,
                'hsn' => $product->hsn_code ?? ''
            ];
        }

        $data = [
            'order_id' => $order->code,
            'order_date' => date('Y-m-d H:i', $order->date),
            'pickup_location' => $pickupLocation,
            'channel_id' => '',
            'comment' => 'Order from Cloud Mart',
            'billing_customer_name' => $order->user->name,
            'billing_last_name' => '',
            'billing_address' => $toAddress['address'],
            'billing_address_2' => '',
            'billing_city' => $toAddress['city'],
            'billing_pincode' => $toAddress['postal_code'],
            'billing_state' => $toAddress['state'],
            'billing_country' => $toAddress['country'],
            'billing_email' => $order->user->email,
            'billing_phone' => $toAddress['phone'],
            'shipping_is_billing' => true,
            'shipping_customer_name' => '',
            'shipping_last_name' => '',
            'shipping_address' => '',
            'shipping_address_2' => '',
            'shipping_city' => '',
            'shipping_pincode' => '',
            'shipping_country' => '',
            'shipping_state' => '',
            'shipping_email' => '',
            'shipping_phone' => '',
            'order_items' => $orderItems,
            'payment_method' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Prepaid',
            'shipping_charges' => 0,
            'giftwrap_charges' => 0,
            'transaction_charges' => 0,
            'total_discount' => 0,
            'sub_total' => $order->orderDetails->sum('price'),
            'length' => $totalLength,
            'breadth' => $totalWidth,
            'height' => $totalHeight,
            'weight' => $totalWeight
        ];

        try {
            Log::info('Shiprocket Order Creation Request', [
                'url' => $this->baseUrl . '/orders/create/adhoc',
                'data' => $data
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/orders/create/adhoc', $data);

            Log::info('Shiprocket Order Creation Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData['order_id'])) {
                    return [
                        'success' => true,
                        'tracking_id' => $responseData['shipment_id'] ?? null,
                        'shipment_id' => $responseData['order_id'],
                        'response' => json_encode($responseData)
                    ];
                }
            }

            Log::error('Shiprocket Order Creation Failed: ' . $response->body());
            return [
                'success' => false,
                'message' => 'Failed to create order with Shiprocket: ' . $response->body()
            ];

        } catch (\Exception $e) {
            Log::error('Shiprocket Order Creation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error creating order with Shiprocket: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Track a shipment with Shiprocket
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [
                'tracking_id' => $trackingId,
                'status' => 'unknown',
                'message' => 'Authentication failed'
            ];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/courier/track/shipment/' . $trackingId);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['tracking_data'])) {
                    $trackingData = $data['tracking_data'];

                    return [
                        'tracking_id' => $trackingId,
                        'status' => $trackingData['track_status'] ?? 'unknown',
                        'current_location' => $trackingData['current_location'] ?? 'Unknown',
                        'expected_delivery' => $trackingData['edd'] ?? null,
                        'tracking_url' => 'https://shiprocket.co/tracking/' . $trackingId,
                        'tracking_data' => $trackingData
                    ];
                }
            }

            return [
                'tracking_id' => $trackingId,
                'status' => 'unknown',
                'message' => 'No tracking data found'
            ];

        } catch (\Exception $e) {
            Log::error('Shiprocket Tracking Error: ' . $e->getMessage());
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Error tracking shipment: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get available courier companies for a route
     *
     * @param array $fromAddress
     * @param array $toAddress
     * @param float $weight
     * @param array $dimensions
     * @param bool $cod
     * @return array
     */
    public function getAvailableCouriers(array $fromAddress, array $toAddress, float $weight = 1.0, array $dimensions = [], bool $cod = false): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [];
        }

        $defaultDimensions = [
            'length' => 10,
            'width' => 10,
            'height' => 5
        ];

        $dimensions = array_merge($defaultDimensions, $dimensions);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/courier/serviceability', [
                'pickup_postcode' => $fromAddress['postal_code'],
                'delivery_postcode' => $toAddress['postal_code'],
                'weight' => $weight,
                'length' => $dimensions['length'],
                'breadth' => $dimensions['width'],
                'height' => $dimensions['height'],
                'cod' => $cod ? 1 : 0
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['available_courier_companies'])) {
                    return $data['data']['available_courier_companies'];
                }
            }

            Log::error('Shiprocket Get Couriers Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Get Couriers Error: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * Get pickup locations from Shiprocket
     *
     * @return array
     */
    public function getPickupLocations(): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/settings/company/pickup');

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['shipping_address'])) {
                    return $data['data']['shipping_address'];
                }
            }

            Log::error('Shiprocket Get Pickup Locations Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Get Pickup Locations Error: ' . $e->getMessage());
        }

        return [];
    }
}
