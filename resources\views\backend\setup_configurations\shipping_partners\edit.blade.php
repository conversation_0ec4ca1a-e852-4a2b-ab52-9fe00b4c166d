@extends('backend.layouts.app')

@section('content')

<div class="aiz-titlebar text-left mt-2 mb-3">
    <h5 class="mb-0 h6">{{translate('Shipping Partner Information')}}</h5>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-body p-0">
                <form class="p-4" action="{{ route('shipping_partners.update', $shippingPartner->id) }}" method="POST">
                    <input name="_method" type="hidden" value="PATCH">
                    @csrf
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="name">{{translate('Name')}}</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" name="name" value="{{ $shippingPartner->name }}" readonly>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="code">{{translate('Code')}}</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" name="code" value="{{ $shippingPartner->code }}" readonly>
                        </div>
                    </div>
                    @php
                        $credentialLabels = [
                            'shiprocket' => ['key' => 'Email', 'secret' => 'Password', 'key_type' => 'email', 'secret_type' => 'password'],
                            'delhivery' => ['key' => 'API Token', 'secret' => 'Client ID', 'key_type' => 'text', 'secret_type' => 'text'],
                            'ecom_express' => ['key' => 'Username', 'secret' => 'Password', 'key_type' => 'text', 'secret_type' => 'password']
                        ];
                        $labels = $credentialLabels[$shippingPartner->code] ?? ['key' => 'API Key', 'secret' => 'API Secret', 'key_type' => 'text', 'secret_type' => 'text'];
                    @endphp

                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="api_key">{{translate($labels['key'])}}</label>
                        <div class="col-sm-9">
                            <input type="{{ $labels['key_type'] }}" class="form-control" name="api_key" value="{{ $shippingPartner->api_key }}"
                                   placeholder="Enter your {{ $shippingPartner->name }} {{ strtolower($labels['key']) }}" required>
                            <small class="text-muted">{{translate('Your ' . $shippingPartner->name . ' account ' . strtolower($labels['key']))}}</small>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="api_secret">{{translate($labels['secret'])}}</label>
                        <div class="col-sm-9">
                            <input type="{{ $labels['secret_type'] }}" class="form-control" name="api_secret" value="{{ $shippingPartner->api_secret }}"
                                   placeholder="Enter your {{ $shippingPartner->name }} {{ strtolower($labels['secret']) }}" required>
                            <small class="text-muted">{{translate('Your ' . $shippingPartner->name . ' account ' . strtolower($labels['secret']))}}</small>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="api_url">{{translate('API URL')}}</label>
                        <div class="col-sm-9">
                            @php
                                $predefinedUrls = [
                                    'shiprocket' => 'https://apiv2.shiprocket.in/v1/external',
                                    'delhivery' => 'https://track.delhivery.com/api',
                                    'ecom_express' => 'https://api.ecomexpress.in'
                                ];
                                $currentUrl = $predefinedUrls[$shippingPartner->code] ?? $shippingPartner->api_url;
                            @endphp
                            <input type="text" class="form-control" name="api_url" value="{{ $currentUrl }}" readonly>
                            <small class="text-muted">{{translate('API URL is automatically configured for this shipping partner')}}</small>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="is_active">{{translate('Status')}}</label>
                        <div class="col-sm-9">
                            <label class="aiz-switch aiz-switch-success mb-0">
                                <input type="checkbox" name="is_active" value="1" @if($shippingPartner->is_active) checked @endif>
                                <span></span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="use_shipping_rate_calculator">{{translate('Use Shipping Rate Calculator')}}</label>
                        <div class="col-sm-9">
                            <label class="aiz-switch aiz-switch-success mb-0">
                                <input type="checkbox" name="use_shipping_rate_calculator" value="1" @if($configuration->use_shipping_rate_calculator) checked @endif>
                                <span></span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-from-label" for="cloudmart_fulfillment_address">{{translate('Cloud Mart Fulfillment Address')}}</label>
                        <div class="col-sm-9">
                            <textarea class="form-control" name="cloudmart_fulfillment_address" rows="5" required>{{ $configuration->cloudmart_fulfillment_address }}</textarea>
                            <small class="text-muted">{{translate('Enter the address in JSON format. Example: {"name":"Cloud Mart","address":"123 Main St","city":"City","state":"State","country":"Country","postal_code":"12345","phone":"1234567890"}')}}</small>
                        </div>
                    </div>
                    @if($shippingPartner->code == 'shiprocket')
                        <div class="form-group row">
                            <div class="col-sm-9 offset-sm-3">
                                <button type="button" class="btn btn-info" onclick="testConnection()">{{translate('Test Connection')}}</button>
                                <small class="text-muted d-block mt-1">{{translate('Test your Shiprocket credentials before saving')}}</small>
                            </div>
                        </div>
                    @endif
                    <div class="form-group mb-0 text-right">
                        <button type="submit" class="btn btn-primary">{{translate('Save')}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection
