<?php

namespace App\Http\Controllers;

use App\Models\ShippingPartner;
use App\Models\ShippingConfiguration;
use App\Services\Shipping\ShippingServiceFactory;
use Illuminate\Http\Request;

class ShippingPartnerController extends Controller
{
    public function __construct() {
        // Staff Permission Check
        $this->middleware(['permission:shipping_configuration'])->only('index','edit','update');
    }
    
    /**
     * Display a listing of the shipping partners.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $shippingPartners = ShippingPartner::all();
        return view('backend.setup_configurations.shipping_partners.index', compact('shippingPartners'));
    }
    
    /**
     * Show the form for editing the specified shipping partner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);
        $configuration = ShippingConfiguration::where('shipping_partner_id', $id)->first();
        
        if (!$configuration) {
            $configuration = new ShippingConfiguration();
            $configuration->shipping_partner_id = $id;
            $configuration->save();
        }
        
        return view('backend.setup_configurations.shipping_partners.edit', compact('shippingPartner', 'configuration'));
    }
    
    /**
     * Update the specified shipping partner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);
        
        $shippingPartner->api_key = $request->api_key;
        $shippingPartner->api_secret = $request->api_secret;
        $shippingPartner->api_url = $request->api_url;
        $shippingPartner->is_active = $request->is_active ? 1 : 0;
        $shippingPartner->save();
        
        $configuration = ShippingConfiguration::where('shipping_partner_id', $id)->first();
        if (!$configuration) {
            $configuration = new ShippingConfiguration();
            $configuration->shipping_partner_id = $id;
        }
        
        $configuration->is_active = $request->is_active ? 1 : 0;
        $configuration->use_shipping_rate_calculator = $request->use_shipping_rate_calculator ? 1 : 0;
        $configuration->cloudmart_fulfillment_address = $request->cloudmart_fulfillment_address;
        $configuration->save();
        
        flash(translate('Shipping partner configuration has been updated successfully'))->success();
        return redirect()->route('shipping_partners.index');
    }
    
    /**
     * Update the status of the specified shipping partner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        $shippingPartner = ShippingPartner::findOrFail($request->id);
        $shippingPartner->is_active = $request->status;
        
        if ($shippingPartner->save()) {
            $configuration = ShippingConfiguration::where('shipping_partner_id', $request->id)->first();
            if ($configuration) {
                $configuration->is_active = $request->status;
                $configuration->save();
            }
            
            return 1;
        }
        
        return 0;
    }

    /**
     * Test connection with shipping partner
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function testConnection(Request $request)
    {
        $shippingPartner = ShippingPartner::findOrFail($request->id);

        // Temporarily update credentials for testing
        $originalApiKey = $shippingPartner->api_key;
        $originalApiSecret = $shippingPartner->api_secret;

        $shippingPartner->api_key = $request->api_key;
        $shippingPartner->api_secret = $request->api_secret;

        try {
            $shippingService = ShippingServiceFactory::getService($shippingPartner);

            // For Shiprocket, test authentication
            if ($shippingPartner->code == 'shiprocket') {
                $reflection = new \ReflectionClass($shippingService);
                $method = $reflection->getMethod('getAuthToken');
                $method->setAccessible(true);
                $token = $method->invoke($shippingService);

                if ($token) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Connection successful! Shiprocket credentials are valid.'
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Connection failed! Please check your email and password.'
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Connection test completed.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ]);
        } finally {
            // Restore original credentials
            $shippingPartner->api_key = $originalApiKey;
            $shippingPartner->api_secret = $originalApiSecret;
        }
    }

    /**
     * Show test page for shipping partner
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function test($id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);

        if ($shippingPartner->code !== 'shiprocket') {
            flash(translate('Test page is only available for Shiprocket'))->error();
            return redirect()->route('shipping_partners.index');
        }

        if (!$shippingPartner->is_active || !$shippingPartner->api_key || !$shippingPartner->api_secret) {
            flash(translate('Please configure and activate Shiprocket first'))->error();
            return redirect()->route('shipping_partners.edit', $id);
        }

        try {
            $shippingService = ShippingServiceFactory::getService($shippingPartner);

            // Get pickup locations
            $pickupLocations = $shippingService->getPickupLocations();

            // Test serviceability (example route)
            $testFromAddress = [
                'postal_code' => '110001' // Delhi
            ];
            $testToAddress = [
                'postal_code' => '400001' // Mumbai
            ];

            $availableCouriers = $shippingService->getAvailableCouriers(
                $testFromAddress,
                $testToAddress,
                1.0,
                ['length' => 10, 'width' => 10, 'height' => 5],
                false
            );

            return view('backend.setup_configurations.shipping_partners.test', compact(
                'shippingPartner',
                'pickupLocations',
                'availableCouriers'
            ));

        } catch (\Exception $e) {
            flash(translate('Error testing Shiprocket: ' . $e->getMessage()))->error();
            return redirect()->route('shipping_partners.edit', $id);
        }
    }
}
