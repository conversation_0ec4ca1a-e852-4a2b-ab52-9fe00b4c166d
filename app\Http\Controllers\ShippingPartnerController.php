<?php

namespace App\Http\Controllers;

use App\Models\ShippingPartner;
use App\Models\ShippingConfiguration;
use Illuminate\Http\Request;

class ShippingPartnerController extends Controller
{
    public function __construct() {
        // Staff Permission Check
        $this->middleware(['permission:shipping_configuration'])->only('index','edit','update');
    }
    
    /**
     * Display a listing of the shipping partners.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $shippingPartners = ShippingPartner::all();
        return view('backend.setup_configurations.shipping_partners.index', compact('shippingPartners'));
    }
    
    /**
     * Show the form for editing the specified shipping partner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);
        $configuration = ShippingConfiguration::where('shipping_partner_id', $id)->first();
        
        if (!$configuration) {
            $configuration = new ShippingConfiguration();
            $configuration->shipping_partner_id = $id;
            $configuration->save();
        }
        
        return view('backend.setup_configurations.shipping_partners.edit', compact('shippingPartner', 'configuration'));
    }
    
    /**
     * Update the specified shipping partner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);

        $shippingPartner->api_key = $request->api_key;
        $shippingPartner->api_secret = $request->api_secret;

        // Set predefined API URLs for specific shipping partners
        if ($shippingPartner->code == 'shiprocket') {
            $shippingPartner->api_url = 'https://apiv2.shiprocket.in/v1/external';
        } elseif ($shippingPartner->code == 'delhivery') {
            $shippingPartner->api_url = 'https://track.delhivery.com/api';
        } elseif ($shippingPartner->code == 'ecom_express') {
            $shippingPartner->api_url = 'https://api.ecomexpress.in';
        } else {
            $shippingPartner->api_url = $request->api_url;
        }

        $shippingPartner->is_active = $request->is_active ? 1 : 0;
        $shippingPartner->save();
        
        $configuration = ShippingConfiguration::where('shipping_partner_id', $id)->first();
        if (!$configuration) {
            $configuration = new ShippingConfiguration();
            $configuration->shipping_partner_id = $id;
        }
        
        $configuration->is_active = $request->is_active ? 1 : 0;
        $configuration->use_shipping_rate_calculator = $request->use_shipping_rate_calculator ? 1 : 0;
        $configuration->cloudmart_fulfillment_address = $request->cloudmart_fulfillment_address;
        $configuration->save();
        
        flash(translate('Shipping partner configuration has been updated successfully'))->success();
        return redirect()->route('shipping_partners.index');
    }
    
    /**
     * Update the status of the specified shipping partner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        $shippingPartner = ShippingPartner::findOrFail($request->id);
        $shippingPartner->is_active = $request->status;
        
        if ($shippingPartner->save()) {
            $configuration = ShippingConfiguration::where('shipping_partner_id', $request->id)->first();
            if ($configuration) {
                $configuration->is_active = $request->status;
                $configuration->save();
            }
            
            return 1;
        }
        
        return 0;
    }
}
