1749969621O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:195:{i:0;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:4:"type";s:21:"home_default_currency";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:05:52";s:10:"updated_at";s:19:"2019-01-28 06:56:53";}s:11:" * original";a:6:{s:2:"id";i:1;s:4:"type";s:21:"home_default_currency";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:05:52";s:10:"updated_at";s:19:"2019-01-28 06:56:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:4:"type";s:23:"system_default_currency";s:5:"value";s:2:"28";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:06:58";s:10:"updated_at";s:19:"2023-12-19 17:28:29";}s:11:" * original";a:6:{s:2:"id";i:2;s:4:"type";s:23:"system_default_currency";s:5:"value";s:2:"28";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:06:58";s:10:"updated_at";s:19:"2023-12-19 17:28:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:3;s:4:"type";s:15:"currency_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2018-10-17 08:31:59";}s:11:" * original";a:6:{s:2:"id";i:3;s:4:"type";s:15:"currency_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2018-10-17 08:31:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:4:"type";s:13:"symbol_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2019-01-20 07:40:55";}s:11:" * original";a:6:{s:2:"id";i:4;s:4:"type";s:13:"symbol_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2019-01-20 07:40:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:5;s:4:"type";s:14:"no_of_decimals";s:5:"value";s:1:"2";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2020-03-04 06:27:16";}s:11:" * original";a:6:{s:2:"id";i:5;s:4:"type";s:14:"no_of_decimals";s:5:"value";s:1:"2";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2020-03-04 06:27:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:6;s:4:"type";s:18:"product_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 07:08:37";s:10:"updated_at";s:19:"2019-02-04 06:41:41";}s:11:" * original";a:6:{s:2:"id";i:6;s:4:"type";s:18:"product_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 07:08:37";s:10:"updated_at";s:19:"2019-02-04 06:41:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:6;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:7;s:4:"type";s:24:"vendor_system_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:16";s:10:"updated_at";s:19:"2024-11-13 15:46:22";}s:11:" * original";a:6:{s:2:"id";i:7;s:4:"type";s:24:"vendor_system_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:16";s:10:"updated_at";s:19:"2024-11-13 15:46:22";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:7;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:8;s:4:"type";s:12:"show_vendors";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:47";s:10:"updated_at";s:19:"2019-02-04 06:41:13";}s:11:" * original";a:6:{s:2:"id";i:8;s:4:"type";s:12:"show_vendors";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:47";s:10:"updated_at";s:19:"2019-02-04 06:41:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:8;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:9;s:4:"type";s:14:"paypal_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:15:16";s:10:"updated_at";s:19:"2019-01-31 10:39:10";}s:11:" * original";a:6:{s:2:"id";i:9;s:4:"type";s:14:"paypal_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:15:16";s:10:"updated_at";s:19:"2019-01-31 10:39:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:9;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:10;s:4:"type";s:14:"stripe_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:15:47";s:10:"updated_at";s:19:"2018-11-14 07:21:51";}s:11:" * original";a:6:{s:2:"id";i:10;s:4:"type";s:14:"stripe_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:15:47";s:10:"updated_at";s:19:"2018-11-14 07:21:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:10;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:11;s:4:"type";s:12:"cash_payment";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:05";s:10:"updated_at";s:19:"2019-01-24 09:10:18";}s:11:" * original";a:6:{s:2:"id";i:11;s:4:"type";s:12:"cash_payment";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:05";s:10:"updated_at";s:19:"2019-01-24 09:10:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:11;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:12;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:27";s:10:"updated_at";s:19:"2019-03-05 11:11:36";}s:11:" * original";a:6:{s:2:"id";i:12;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:27";s:10:"updated_at";s:19:"2019-03-05 11:11:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:12;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:13;s:4:"type";s:12:"best_selling";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-12-24 13:43:44";s:10:"updated_at";s:19:"2019-02-14 10:59:13";}s:11:" * original";a:6:{s:2:"id";i:13;s:4:"type";s:12:"best_selling";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-12-24 13:43:44";s:10:"updated_at";s:19:"2019-02-14 10:59:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:13;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:14;s:4:"type";s:14:"paypal_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2019-01-16 18:14:18";}s:11:" * original";a:6:{s:2:"id";i:14;s:4:"type";s:14:"paypal_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2019-01-16 18:14:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:14;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:15;s:4:"type";s:18:"sslcommerz_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2024-04-20 16:54:18";}s:11:" * original";a:6:{s:2:"id";i:15;s:4:"type";s:18:"sslcommerz_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2024-04-20 16:54:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:15;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:16;s:4:"type";s:18:"sslcommerz_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-24 15:09:07";s:10:"updated_at";s:19:"2019-01-29 11:43:46";}s:11:" * original";a:6:{s:2:"id";i:16;s:4:"type";s:18:"sslcommerz_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-24 15:09:07";s:10:"updated_at";s:19:"2019-01-29 11:43:46";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:16;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:17;s:4:"type";s:17:"vendor_commission";s:5:"value";s:2:"10";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-31 11:48:04";s:10:"updated_at";s:19:"2024-12-03 02:36:20";}s:11:" * original";a:6:{s:2:"id";i:17;s:4:"type";s:17:"vendor_commission";s:5:"value";s:2:"10";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-31 11:48:04";s:10:"updated_at";s:19:"2024-12-03 02:36:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:17;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:18;s:4:"type";s:17:"verification_form";s:5:"value";s:257:"[{"type":"text","label":"Your name"},{"type":"text","label":"Shop name"},{"type":"text","label":"Email"},{"type":"text","label":"License No"},{"type":"text","label":"Full Address"},{"type":"text","label":"Phone Number"},{"type":"file","label":"Tax Papers"}]";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-03 17:06:58";s:10:"updated_at";s:19:"2019-02-16 11:44:42";}s:11:" * original";a:6:{s:2:"id";i:18;s:4:"type";s:17:"verification_form";s:5:"value";s:257:"[{"type":"text","label":"Your name"},{"type":"text","label":"Shop name"},{"type":"text","label":"Email"},{"type":"text","label":"License No"},{"type":"text","label":"Full Address"},{"type":"text","label":"Phone Number"},{"type":"file","label":"Tax Papers"}]";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-03 17:06:58";s:10:"updated_at";s:19:"2019-02-16 11:44:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:18;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:19;s:4:"type";s:16:"google_analytics";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-06 17:52:35";s:10:"updated_at";s:19:"2019-02-06 17:52:35";}s:11:" * original";a:6:{s:2:"id";i:19;s:4:"type";s:16:"google_analytics";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-06 17:52:35";s:10:"updated_at";s:19:"2019-02-06 17:52:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:19;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:20;s:4:"type";s:14:"facebook_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:21:59";s:10:"updated_at";s:19:"2024-11-29 19:32:15";}s:11:" * original";a:6:{s:2:"id";i:20;s:4:"type";s:14:"facebook_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:21:59";s:10:"updated_at";s:19:"2024-11-29 19:32:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:20;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:21;s:4:"type";s:12:"google_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:10";s:10:"updated_at";s:19:"2025-03-12 23:22:54";}s:11:" * original";a:6:{s:2:"id";i:21;s:4:"type";s:12:"google_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:10";s:10:"updated_at";s:19:"2025-03-12 23:22:54";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:21;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:22;s:4:"type";s:13:"twitter_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:20";s:10:"updated_at";s:19:"2019-02-08 08:02:56";}s:11:" * original";a:6:{s:2:"id";i:22;s:4:"type";s:13:"twitter_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:20";s:10:"updated_at";s:19:"2019-02-08 08:02:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:22;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:23;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 17:08:17";}s:11:" * original";a:6:{s:2:"id";i:23;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 17:08:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:23;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:24;s:4:"type";s:17:"payumoney_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 11:09:18";}s:11:" * original";a:6:{s:2:"id";i:24;s:4:"type";s:17:"payumoney_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 11:09:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:24;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:36;s:4:"type";s:13:"facebook_chat";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-15 17:15:04";s:10:"updated_at";s:19:"2019-04-15 17:15:04";}s:11:" * original";a:6:{s:2:"id";i:36;s:4:"type";s:13:"facebook_chat";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-15 17:15:04";s:10:"updated_at";s:19:"2019-04-15 17:15:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:25;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:37;s:4:"type";s:18:"email_verification";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-30 13:00:07";s:10:"updated_at";s:19:"2019-04-30 13:00:07";}s:11:" * original";a:6:{s:2:"id";i:37;s:4:"type";s:18:"email_verification";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-30 13:00:07";s:10:"updated_at";s:19:"2019-04-30 13:00:07";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:26;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:38;s:4:"type";s:13:"wallet_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-05-19 13:35:44";s:10:"updated_at";s:19:"2024-12-03 02:08:26";}s:11:" * original";a:6:{s:2:"id";i:38;s:4:"type";s:13:"wallet_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-05-19 13:35:44";s:10:"updated_at";s:19:"2024-12-03 02:08:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:27;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:39;s:4:"type";s:13:"coupon_system";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2024-11-04 15:07:52";}s:11:" * original";a:6:{s:2:"id";i:39;s:4:"type";s:13:"coupon_system";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2024-11-04 15:07:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:28;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:40;s:4:"type";s:15:"current_version";s:5:"value";s:3:"8.2";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2019-06-11 15:16:18";}s:11:" * original";a:6:{s:2:"id";i:40;s:4:"type";s:15:"current_version";s:5:"value";s:3:"8.2";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2019-06-11 15:16:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:29;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:41;s:4:"type";s:17:"instamojo_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:03";s:10:"updated_at";s:19:"2019-07-06 15:28:03";}s:11:" * original";a:6:{s:2:"id";i:41;s:4:"type";s:17:"instamojo_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:03";s:10:"updated_at";s:19:"2019-07-06 15:28:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:30;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:42;s:4:"type";s:17:"instamojo_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:43";s:10:"updated_at";s:19:"2024-04-20 16:54:48";}s:11:" * original";a:6:{s:2:"id";i:42;s:4:"type";s:17:"instamojo_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:43";s:10:"updated_at";s:19:"2024-04-20 16:54:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:31;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:43;s:4:"type";s:8:"razorpay";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:43";s:10:"updated_at";s:19:"2024-01-19 23:25:06";}s:11:" * original";a:6:{s:2:"id";i:43;s:4:"type";s:8:"razorpay";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:43";s:10:"updated_at";s:19:"2024-01-19 23:25:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:32;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:44;s:4:"type";s:8:"paystack";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-21 18:30:38";s:10:"updated_at";s:19:"2019-07-21 18:30:38";}s:11:" * original";a:6:{s:2:"id";i:44;s:4:"type";s:8:"paystack";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-21 18:30:38";s:10:"updated_at";s:19:"2019-07-21 18:30:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:33;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:45;s:4:"type";s:12:"pickup_point";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:20:39";s:10:"updated_at";s:19:"2024-12-03 02:08:12";}s:11:" * original";a:6:{s:2:"id";i:45;s:4:"type";s:12:"pickup_point";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:20:39";s:10:"updated_at";s:19:"2024-12-03 02:08:12";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:34;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:46;s:4:"type";s:16:"maintenance_mode";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:04";s:10:"updated_at";s:19:"2019-10-17 17:21:04";}s:11:" * original";a:6:{s:2:"id";i:46;s:4:"type";s:16:"maintenance_mode";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:04";s:10:"updated_at";s:19:"2019-10-17 17:21:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:35;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:47;s:4:"type";s:8:"voguepay";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:24";s:10:"updated_at";s:19:"2019-10-17 17:21:24";}s:11:" * original";a:6:{s:2:"id";i:47;s:4:"type";s:8:"voguepay";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:24";s:10:"updated_at";s:19:"2019-10-17 17:21:24";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:36;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:48;s:4:"type";s:16:"voguepay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:38";s:10:"updated_at";s:19:"2019-10-17 17:21:38";}s:11:" * original";a:6:{s:2:"id";i:48;s:4:"type";s:16:"voguepay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:38";s:10:"updated_at";s:19:"2019-10-17 17:21:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:37;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:50;s:4:"type";s:24:"category_wise_commission";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:52:47";s:10:"updated_at";s:19:"2024-12-03 02:36:17";}s:11:" * original";a:6:{s:2:"id";i:50;s:4:"type";s:24:"category_wise_commission";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:52:47";s:10:"updated_at";s:19:"2024-12-03 02:36:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:38;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:51;s:4:"type";s:19:"conversation_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:53:21";s:10:"updated_at";s:19:"2020-01-21 12:53:21";}s:11:" * original";a:6:{s:2:"id";i:51;s:4:"type";s:19:"conversation_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:53:21";s:10:"updated_at";s:19:"2020-01-21 12:53:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:39;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:52;s:4:"type";s:21:"guest_checkout_active";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 13:06:38";s:10:"updated_at";s:19:"2020-01-22 13:06:38";}s:11:" * original";a:6:{s:2:"id";i:52;s:4:"type";s:21:"guest_checkout_active";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 13:06:38";s:10:"updated_at";s:19:"2020-01-22 13:06:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:40;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:53;s:4:"type";s:14:"facebook_pixel";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 17:13:58";s:10:"updated_at";s:19:"2020-01-22 17:13:58";}s:11:" * original";a:6:{s:2:"id";i:53;s:4:"type";s:14:"facebook_pixel";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 17:13:58";s:10:"updated_at";s:19:"2020-01-22 17:13:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:41;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:55;s:4:"type";s:18:"classified_product";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-05-13 18:31:05";s:10:"updated_at";s:19:"2023-12-19 17:26:24";}s:11:" * original";a:6:{s:2:"id";i:55;s:4:"type";s:18:"classified_product";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-05-13 18:31:05";s:10:"updated_at";s:19:"2023-12-19 17:26:24";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:42;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:56;s:4:"type";s:25:"pos_activation_for_seller";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-06-11 15:15:02";s:10:"updated_at";s:19:"2020-06-11 15:15:02";}s:11:" * original";a:6:{s:2:"id";i:56;s:4:"type";s:25:"pos_activation_for_seller";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-06-11 15:15:02";s:10:"updated_at";s:19:"2020-06-11 15:15:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:43;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:57;s:4:"type";s:13:"shipping_type";s:5:"value";s:18:"area_wise_shipping";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2024-01-20 00:00:10";}s:11:" * original";a:6:{s:2:"id";i:57;s:4:"type";s:13:"shipping_type";s:5:"value";s:18:"area_wise_shipping";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2024-01-20 00:00:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:44;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:58;s:4:"type";s:23:"flat_rate_shipping_cost";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:11:" * original";a:6:{s:2:"id";i:58;s:4:"type";s:23:"flat_rate_shipping_cost";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:45;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:59;s:4:"type";s:19:"shipping_cost_admin";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:11:" * original";a:6:{s:2:"id";i:59;s:4:"type";s:19:"shipping_cost_admin";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:46;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:60;s:4:"type";s:15:"payhere_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-30 23:53:53";s:10:"updated_at";s:19:"2020-07-30 23:53:53";}s:11:" * original";a:6:{s:2:"id";i:60;s:4:"type";s:15:"payhere_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-30 23:53:53";s:10:"updated_at";s:19:"2020-07-30 23:53:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:47;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:61;s:4:"type";s:7:"payhere";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-30 23:53:53";s:10:"updated_at";s:19:"2020-07-30 23:53:53";}s:11:" * original";a:6:{s:2:"id";i:61;s:4:"type";s:7:"payhere";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-30 23:53:53";s:10:"updated_at";s:19:"2020-07-30 23:53:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:48;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:62;s:4:"type";s:16:"google_recaptcha";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-08-17 12:43:37";s:10:"updated_at";s:19:"2020-08-17 12:43:37";}s:11:" * original";a:6:{s:2:"id";i:62;s:4:"type";s:16:"google_recaptcha";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-08-17 12:43:37";s:10:"updated_at";s:19:"2020-08-17 12:43:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:49;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:63;s:4:"type";s:7:"ngenius";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-09-22 16:28:21";s:10:"updated_at";s:19:"2020-09-22 16:28:21";}s:11:" * original";a:6:{s:2:"id";i:63;s:4:"type";s:7:"ngenius";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-09-22 16:28:21";s:10:"updated_at";s:19:"2020-09-22 16:28:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:50;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:64;s:4:"type";s:11:"header_logo";s:5:"value";s:4:"1145";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-03-15 23:58:10";}s:11:" * original";a:6:{s:2:"id";i:64;s:4:"type";s:11:"header_logo";s:5:"value";s:4:"1145";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-03-15 23:58:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:51;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:65;s:4:"type";s:22:"show_language_switcher";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:65;s:4:"type";s:22:"show_language_switcher";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:52;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:66;s:4:"type";s:22:"show_currency_switcher";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:66;s:4:"type";s:22:"show_currency_switcher";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:53;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:67;s:4:"type";s:13:"header_stikcy";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:67;s:4:"type";s:13:"header_stikcy";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:54;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:68;s:4:"type";s:11:"footer_logo";s:5:"value";s:3:"361";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-05 21:12:37";}s:11:" * original";a:6:{s:2:"id";i:68;s:4:"type";s:11:"footer_logo";s:5:"value";s:3:"361";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-05 21:12:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:55;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:69;s:4:"type";s:20:"about_us_description";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:69;s:4:"type";s:20:"about_us_description";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:56;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:70;s:4:"type";s:15:"contact_address";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:70;s:4:"type";s:15:"contact_address";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:57;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:71;s:4:"type";s:13:"contact_phone";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-03 04:35:43";}s:11:" * original";a:6:{s:2:"id";i:71;s:4:"type";s:13:"contact_phone";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-03 04:35:43";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:58;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:72;s:4:"type";s:13:"contact_email";s:5:"value";s:23:"<EMAIL>";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-31 23:02:27";}s:11:" * original";a:6:{s:2:"id";i:72;s:4:"type";s:13:"contact_email";s:5:"value";s:23:"<EMAIL>";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-31 23:02:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:59;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:73;s:4:"type";s:17:"widget_one_labels";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:73;s:4:"type";s:17:"widget_one_labels";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:60;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:74;s:4:"type";s:16:"widget_one_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:74;s:4:"type";s:16:"widget_one_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:61;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:75;s:4:"type";s:10:"widget_one";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:75;s:4:"type";s:10:"widget_one";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:62;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:76;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:76;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:63;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:77;s:4:"type";s:17:"show_social_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-22 20:09:32";}s:11:" * original";a:6:{s:2:"id";i:77;s:4:"type";s:17:"show_social_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-22 20:09:32";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:64;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:78;s:4:"type";s:13:"facebook_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:11:" * original";a:6:{s:2:"id";i:78;s:4:"type";s:13:"facebook_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:65;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:79;s:4:"type";s:12:"twitter_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:11:" * original";a:6:{s:2:"id";i:79;s:4:"type";s:12:"twitter_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:66;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:80;s:4:"type";s:14:"instagram_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:11:" * original";a:6:{s:2:"id";i:80;s:4:"type";s:14:"instagram_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:67;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:81;s:4:"type";s:12:"youtube_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:11:" * original";a:6:{s:2:"id";i:81;s:4:"type";s:12:"youtube_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-11 03:18:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:68;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:82;s:4:"type";s:13:"linkedin_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-11-29 17:00:43";}s:11:" * original";a:6:{s:2:"id";i:82;s:4:"type";s:13:"linkedin_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-11-29 17:00:43";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:69;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:83;s:4:"type";s:21:"payment_method_images";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:83;s:4:"type";s:21:"payment_method_images";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:70;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:84;s:4:"type";s:18:"home_slider_images";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:84;s:4:"type";s:18:"home_slider_images";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:71;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:85;s:4:"type";s:17:"home_slider_links";s:5:"value";s:151:"[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-28 01:05:28";}s:11:" * original";a:6:{s:2:"id";i:85;s:4:"type";s:17:"home_slider_links";s:5:"value";s:151:"[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-28 01:05:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:72;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:86;s:4:"type";s:19:"home_banner1_images";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:86;s:4:"type";s:19:"home_banner1_images";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:73;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:87;s:4:"type";s:18:"home_banner1_links";s:5:"value";s:24:"["https:\/\/zestpik.in"]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-10-30 17:19:32";}s:11:" * original";a:6:{s:2:"id";i:87;s:4:"type";s:18:"home_banner1_links";s:5:"value";s:24:"["https:\/\/zestpik.in"]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-10-30 17:19:32";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:74;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:88;s:4:"type";s:19:"home_banner2_images";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:88;s:4:"type";s:19:"home_banner2_images";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:75;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:89;s:4:"type";s:18:"home_banner2_links";s:5:"value";s:24:"["https:\/\/zestpik.in"]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-11-04 21:01:32";}s:11:" * original";a:6:{s:2:"id";i:89;s:4:"type";s:18:"home_banner2_links";s:5:"value";s:24:"["https:\/\/zestpik.in"]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-11-04 21:01:32";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:76;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:90;s:4:"type";s:15:"home_categories";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-10-30 16:13:09";}s:11:" * original";a:6:{s:2:"id";i:90;s:4:"type";s:15:"home_categories";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-10-30 16:13:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:77;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:91;s:4:"type";s:16:"top10_categories";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:91;s:4:"type";s:16:"top10_categories";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:78;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:92;s:4:"type";s:12:"top10_brands";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:92;s:4:"type";s:12:"top10_brands";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:79;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:93;s:4:"type";s:12:"website_name";s:5:"value";s:48:"https://cloud-mart.com/ -  Site by Eros Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:38:19";}s:11:" * original";a:6:{s:2:"id";i:93;s:4:"type";s:12:"website_name";s:5:"value";s:48:"https://cloud-mart.com/ -  Site by Eros Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:38:19";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:80;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:94;s:4:"type";s:10:"site_motto";s:5:"value";s:16:"Ecommerce Portal";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-22 22:05:43";}s:11:" * original";a:6:{s:2:"id";i:94;s:4:"type";s:10:"site_motto";s:5:"value";s:16:"Ecommerce Portal";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-22 22:05:43";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:81;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:95;s:4:"type";s:9:"site_icon";s:5:"value";s:3:"331";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:33:26";}s:11:" * original";a:6:{s:2:"id";i:95;s:4:"type";s:9:"site_icon";s:5:"value";s:3:"331";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:33:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:82;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:96;s:4:"type";s:10:"base_color";s:5:"value";s:7:"#686a6b";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-11-25 18:41:27";}s:11:" * original";a:6:{s:2:"id";i:96;s:4:"type";s:10:"base_color";s:5:"value";s:7:"#686a6b";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-11-25 18:41:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:83;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:97;s:4:"type";s:14:"base_hov_color";s:5:"value";s:7:"#e62e04";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:97;s:4:"type";s:14:"base_hov_color";s:5:"value";s:7:"#e62e04";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:84;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:98;s:4:"type";s:10:"meta_title";s:5:"value";s:59:"https://cloud-mart.com/ Fashion Store Site by EROS Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:37:47";}s:11:" * original";a:6:{s:2:"id";i:98;s:4:"type";s:10:"meta_title";s:5:"value";s:59:"https://cloud-mart.com/ Fashion Store Site by EROS Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:37:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:85;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:99;s:4:"type";s:16:"meta_description";s:5:"value";s:59:"https://cloud-mart.com/ Fashion Store Site by EROS Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:37:47";}s:11:" * original";a:6:{s:2:"id";i:99;s:4:"type";s:16:"meta_description";s:5:"value";s:59:"https://cloud-mart.com/ Fashion Store Site by EROS Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:37:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:86;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:100;s:4:"type";s:13:"meta_keywords";s:5:"value";s:59:"https://cloud-mart.com/ Fashion Store Site by EROS Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:37:47";}s:11:" * original";a:6:{s:2:"id";i:100;s:4:"type";s:13:"meta_keywords";s:5:"value";s:59:"https://cloud-mart.com/ Fashion Store Site by EROS Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:37:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:87;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:101;s:4:"type";s:10:"meta_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:101;s:4:"type";s:10:"meta_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:88;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:102;s:4:"type";s:9:"site_name";s:5:"value";s:144:"Cloud-mart.com                                                                                                             Site by Eros Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:38:19";}s:11:" * original";a:6:{s:2:"id";i:102;s:4:"type";s:9:"site_name";s:5:"value";s:144:"Cloud-mart.com                                                                                                             Site by Eros Infotech";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-01-29 16:38:19";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:89;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:103;s:4:"type";s:17:"system_logo_white";s:5:"value";s:3:"362";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-05 02:05:49";}s:11:" * original";a:6:{s:2:"id";i:103;s:4:"type";s:17:"system_logo_white";s:5:"value";s:3:"362";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-05 02:05:49";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:90;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:104;s:4:"type";s:17:"system_logo_black";s:5:"value";s:3:"362";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-05 02:05:49";}s:11:" * original";a:6:{s:2:"id";i:104;s:4:"type";s:17:"system_logo_black";s:5:"value";s:3:"362";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-02-05 02:05:49";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:91;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:105;s:4:"type";s:8:"timezone";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:105;s:4:"type";s:8:"timezone";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:92;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:106;s:4:"type";s:22:"admin_login_background";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:106;s:4:"type";s:22:"admin_login_background";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:93;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:107;s:4:"type";s:14:"iyzico_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2024-04-20 16:54:39";}s:11:" * original";a:6:{s:2:"id";i:107;s:4:"type";s:14:"iyzico_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2024-04-20 16:54:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:94;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:108;s:4:"type";s:6:"iyzico";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2024-01-19 23:24:58";}s:11:" * original";a:6:{s:2:"id";i:108;s:4:"type";s:6:"iyzico";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2024-01-19 23:24:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:95;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:109;s:4:"type";s:17:"decimal_separator";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2020-12-30 22:15:56";}s:11:" * original";a:6:{s:2:"id";i:109;s:4:"type";s:17:"decimal_separator";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2020-12-30 22:15:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:96;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:110;s:4:"type";s:5:"nagad";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2021-01-22 16:00:03";}s:11:" * original";a:6:{s:2:"id";i:110;s:4:"type";s:5:"nagad";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2021-01-22 16:00:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:97;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:111;s:4:"type";s:5:"bkash";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2021-01-22 16:00:03";}s:11:" * original";a:6:{s:2:"id";i:111;s:4:"type";s:5:"bkash";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2021-01-22 16:00:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:98;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:112;s:4:"type";s:13:"bkash_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2024-04-20 16:54:08";}s:11:" * original";a:6:{s:2:"id";i:112;s:4:"type";s:13:"bkash_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2024-04-20 16:54:08";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:99;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:113;s:4:"type";s:18:"header_menu_labels";s:5:"value";s:56:"["Home","Shop","All Categories","About Us","Contact Us"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-01-22 20:08:16";}s:11:" * original";a:6:{s:2:"id";i:113;s:4:"type";s:18:"header_menu_labels";s:5:"value";s:56:"["Home","Shop","All Categories","About Us","Contact Us"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-01-22 20:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:100;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:114;s:4:"type";s:17:"header_menu_links";s:5:"value";s:195:"["https:\/\/cloud-mart.com\/","https:\/\/cloud-mart.com\/search?search_by=shops","https:\/\/cloud-mart.com\/categories","https:\/\/cloud-mart.com\/aboutus","https:\/\/cloud-mart.com\/contact-us"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-01-31 22:59:06";}s:11:" * original";a:6:{s:2:"id";i:114;s:4:"type";s:17:"header_menu_links";s:5:"value";s:195:"["https:\/\/cloud-mart.com\/","https:\/\/cloud-mart.com\/search?search_by=shops","https:\/\/cloud-mart.com\/categories","https:\/\/cloud-mart.com\/aboutus","https:\/\/cloud-mart.com\/contact-us"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-01-31 22:59:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:101;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:115;s:4:"type";s:8:"proxypay";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:11:" * original";a:6:{s:2:"id";i:115;s:4:"type";s:8:"proxypay";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:102;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:116;s:4:"type";s:16:"proxypay_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:11:" * original";a:6:{s:2:"id";i:116;s:4:"type";s:16:"proxypay_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:103;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:117;s:4:"type";s:10:"google_map";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:11:" * original";a:6:{s:2:"id";i:117;s:4:"type";s:10:"google_map";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:104;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:118;s:4:"type";s:15:"google_firebase";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:11:" * original";a:6:{s:2:"id";i:118;s:4:"type";s:15:"google_firebase";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:105;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:119;s:4:"type";s:20:"authorizenet_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2024-04-20 16:55:03";}s:11:" * original";a:6:{s:2:"id";i:119;s:4:"type";s:20:"authorizenet_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2024-04-20 16:55:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:106;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:120;s:4:"type";s:30:"min_order_amount_check_activat";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:120;s:4:"type";s:30:"min_order_amount_check_activat";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:107;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:121;s:4:"type";s:20:"minimum_order_amount";s:5:"value";s:6:"500.00";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2025-03-03 03:36:51";}s:11:" * original";a:6:{s:2:"id";i:121;s:4:"type";s:20:"minimum_order_amount";s:5:"value";s:6:"500.00";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2025-03-03 03:36:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:108;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:122;s:4:"type";s:9:"item_name";s:5:"value";s:9:"eCommerce";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:122;s:4:"type";s:9:"item_name";s:5:"value";s:9:"eCommerce";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:109;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:123;s:4:"type";s:8:"aamarpay";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:123;s:4:"type";s:8:"aamarpay";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:110;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:124;s:4:"type";s:16:"aamarpay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:124;s:4:"type";s:16:"aamarpay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:111;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:125;s:4:"type";s:20:"secondary_base_color";s:5:"value";s:7:"#0a0909";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2024-11-24 17:29:25";}s:11:" * original";a:6:{s:2:"id";i:125;s:4:"type";s:20:"secondary_base_color";s:5:"value";s:7:"#0a0909";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2024-11-24 17:29:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:112;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:126;s:4:"type";s:24:"secondary_base_hov_color";s:5:"value";s:7:"#0f6f41";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:126;s:4:"type";s:24:"secondary_base_hov_color";s:5:"value";s:7:"#0f6f41";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:113;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:127;s:4:"type";s:20:"header_nav_menu_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:127;s:4:"type";s:20:"header_nav_menu_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:114;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:128;s:4:"type";s:15:"homepage_select";s:5:"value";s:7:"suruchi";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2024-09-11 16:03:33";}s:11:" * original";a:6:{s:2:"id";i:128;s:4:"type";s:15:"homepage_select";s:5:"value";s:7:"suruchi";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2024-09-11 16:03:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:115;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:129;s:4:"type";s:22:"todays_deal_section_bg";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 02:59:42";}s:11:" * original";a:6:{s:2:"id";i:129;s:4:"type";s:22:"todays_deal_section_bg";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 02:59:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:116;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:130;s:4:"type";s:28:"todays_deal_section_bg_color";s:5:"value";s:7:"#676565";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:02:28";}s:11:" * original";a:6:{s:2:"id";i:130;s:4:"type";s:28:"todays_deal_section_bg_color";s:5:"value";s:7:"#676565";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:02:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:117;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:131;s:4:"type";s:19:"flash_deal_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:10:26";}s:11:" * original";a:6:{s:2:"id";i:131;s:4:"type";s:19:"flash_deal_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:10:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:118;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:132;s:4:"type";s:24:"flash_deal_bg_full_width";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:09:58";}s:11:" * original";a:6:{s:2:"id";i:132;s:4:"type";s:24:"flash_deal_bg_full_width";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:09:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:119;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:133;s:4:"type";s:27:"flash_deal_banner_menu_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:11:52";}s:11:" * original";a:6:{s:2:"id";i:133;s:4:"type";s:27:"flash_deal_banner_menu_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-20 03:11:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:120;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:134;s:4:"type";s:29:"todays_deal_banner_text_color";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-19 21:19:33";}s:11:" * original";a:6:{s:2:"id";i:134;s:4:"type";s:29:"todays_deal_banner_text_color";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-12-19 21:19:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:121;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:135;s:4:"type";s:23:"coupon_background_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:135;s:4:"type";s:23:"coupon_background_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:122;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:136;s:4:"type";s:22:"admin_login_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:136;s:4:"type";s:22:"admin_login_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:123;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:137;s:4:"type";s:25:"customer_login_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:137;s:4:"type";s:25:"customer_login_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:124;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:138;s:4:"type";s:28:"customer_register_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:138;s:4:"type";s:28:"customer_register_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:125;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:139;s:4:"type";s:23:"seller_login_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:139;s:4:"type";s:23:"seller_login_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:126;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:140;s:4:"type";s:26:"seller_register_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:140;s:4:"type";s:26:"seller_register_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:127;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:141;s:4:"type";s:29:"delivery_boy_login_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:141;s:4:"type";s:29:"delivery_boy_login_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:128;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:142;s:4:"type";s:26:"forgot_password_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:142;s:4:"type";s:26:"forgot_password_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:129;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:143;s:4:"type";s:25:"password_reset_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:11:" * original";a:6:{s:2:"id";i:143;s:4:"type";s:25:"password_reset_page_image";s:5:"value";s:3:"321";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-01-22 21:41:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:130;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:144;s:4:"type";s:30:"phone_number_verify_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:144;s:4:"type";s:30:"phone_number_verify_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:131;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:145;s:4:"type";s:28:"authentication_layout_select";s:5:"value";s:7:"suruchi";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-11-29 18:52:47";}s:11:" * original";a:6:{s:2:"id";i:145;s:4:"type";s:28:"authentication_layout_select";s:5:"value";s:7:"suruchi";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-11-29 18:52:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:132;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:146;s:4:"type";s:24:"flash_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 17:18:55";}s:11:" * original";a:6:{s:2:"id";i:146;s:4:"type";s:24:"flash_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 17:18:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:133;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:147;s:4:"type";s:24:"flash_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:147;s:4:"type";s:24:"flash_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:134;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:148;s:4:"type";s:27:"flash_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:148;s:4:"type";s:27:"flash_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:135;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:149;s:4:"type";s:20:"flash_deal_card_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 16:08:40";}s:11:" * original";a:6:{s:2:"id";i:149;s:4:"type";s:20:"flash_deal_card_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 16:08:40";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:136;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:150;s:4:"type";s:25:"todays_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 17:19:01";}s:11:" * original";a:6:{s:2:"id";i:150;s:4:"type";s:25:"todays_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 17:19:01";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:137;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:151;s:4:"type";s:25:"todays_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:151;s:4:"type";s:25:"todays_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:138;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:152;s:4:"type";s:28:"todays_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:152;s:4:"type";s:28:"todays_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:139;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:153;s:4:"type";s:21:"todays_deal_card_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 16:08:55";}s:11:" * original";a:6:{s:2:"id";i:153;s:4:"type";s:21:"todays_deal_card_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-10-30 16:08:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:140;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:154;s:4:"type";s:25:"new_product_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:154;s:4:"type";s:25:"new_product_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:141;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:155;s:4:"type";s:25:"new_product_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:155;s:4:"type";s:25:"new_product_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:142;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:156;s:4:"type";s:28:"new_product_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:156;s:4:"type";s:28:"new_product_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:143;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:157;s:4:"type";s:21:"new_product_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:157;s:4:"type";s:21:"new_product_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:144;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:158;s:4:"type";s:24:"featured_categories_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-11-29 17:27:51";}s:11:" * original";a:6:{s:2:"id";i:158;s:4:"type";s:24:"featured_categories_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2024-11-29 17:27:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:145;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:159;s:4:"type";s:13:"purchase_code";s:5:"value";s:3:"asd";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 16:52:37";s:10:"updated_at";s:19:"2023-12-19 16:52:37";}s:11:" * original";a:6:{s:2:"id";i:159;s:4:"type";s:13:"purchase_code";s:5:"value";s:3:"asd";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 16:52:37";s:10:"updated_at";s:19:"2023-12-19 16:52:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:146;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:160;s:4:"type";s:17:"flash_deal_banner";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:02:10";s:10:"updated_at";s:19:"2023-12-19 17:02:10";}s:11:" * original";a:6:{s:2:"id";i:160;s:4:"type";s:17:"flash_deal_banner";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:02:10";s:10:"updated_at";s:19:"2023-12-19 17:02:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:147;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:161;s:4:"type";s:23:"flash_deal_banner_small";s:5:"value";s:2:"28";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:02:10";s:10:"updated_at";s:19:"2024-04-08 01:11:30";}s:11:" * original";a:6:{s:2:"id";i:161;s:4:"type";s:23:"flash_deal_banner_small";s:5:"value";s:2:"28";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:02:10";s:10:"updated_at";s:19:"2024-04-08 01:11:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:148;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:162;s:4:"type";s:18:"home_slider_images";s:5:"value";s:181:"["553","551","552","549","550","548","546","547","544","545","543","540","541","542","539","537","538","536","534","535","532","533","531","528","529","530","527","525","526","524"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 17:09:04";s:10:"updated_at";s:19:"2025-02-28 01:05:28";}s:11:" * original";a:6:{s:2:"id";i:162;s:4:"type";s:18:"home_slider_images";s:5:"value";s:181:"["553","551","552","549","550","548","546","547","544","545","543","540","541","542","539","537","538","536","534","535","532","533","531","528","529","530","527","525","526","524"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 17:09:04";s:10:"updated_at";s:19:"2025-02-28 01:05:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:149;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:163;s:4:"type";s:13:"topbar_banner";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2024-11-29 18:58:29";}s:11:" * original";a:6:{s:2:"id";i:163;s:4:"type";s:13:"topbar_banner";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2024-11-29 18:58:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:150;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:164;s:4:"type";s:20:"topbar_banner_medium";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2024-10-30 15:33:00";}s:11:" * original";a:6:{s:2:"id";i:164;s:4:"type";s:20:"topbar_banner_medium";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2024-10-30 15:33:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:151;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:165;s:4:"type";s:19:"topbar_banner_small";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2024-10-30 15:33:00";}s:11:" * original";a:6:{s:2:"id";i:165;s:4:"type";s:19:"topbar_banner_small";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2024-10-30 15:33:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:152;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:166;s:4:"type";s:18:"topbar_banner_link";s:5:"value";s:14:"cloud-mart.com";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2025-01-29 16:57:17";}s:11:" * original";a:6:{s:2:"id";i:166;s:4:"type";s:18:"topbar_banner_link";s:5:"value";s:14:"cloud-mart.com";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2025-01-29 16:57:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:153;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:167;s:4:"type";s:15:"helpline_number";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2025-02-03 04:35:05";}s:11:" * original";a:6:{s:2:"id";i:167;s:4:"type";s:15:"helpline_number";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:10:36";s:10:"updated_at";s:19:"2025-02-03 04:35:05";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:154;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:168;s:4:"type";s:20:"about_us_description";s:5:"value";s:487:"<p class="p1" style="margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-size-adjust: none; font-kerning: auto; font-optical-sizing: auto; font-feature-settings: normal; font-variation-settings: normal; font-variant-position: normal; font-variant-emoji: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: " helvetica="" neue";"=""><b>working s</b></p>";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 17:11:29";s:10:"updated_at";s:19:"2025-02-06 22:00:55";}s:11:" * original";a:6:{s:2:"id";i:168;s:4:"type";s:20:"about_us_description";s:5:"value";s:487:"<p class="p1" style="margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-size-adjust: none; font-kerning: auto; font-optical-sizing: auto; font-feature-settings: normal; font-variation-settings: normal; font-variant-position: normal; font-variant-emoji: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: " helvetica="" neue";"=""><b>working s</b></p>";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 17:11:29";s:10:"updated_at";s:19:"2025-02-06 22:00:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:155;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:169;s:4:"type";s:15:"play_store_link";s:5:"value";s:69:"https://play.google.com/store/apps/details?id=com.cloudmart.cloudmart";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:11:29";s:10:"updated_at";s:19:"2025-01-31 23:01:26";}s:11:" * original";a:6:{s:2:"id";i:169;s:4:"type";s:15:"play_store_link";s:5:"value";s:69:"https://play.google.com/store/apps/details?id=com.cloudmart.cloudmart";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:11:29";s:10:"updated_at";s:19:"2025-01-31 23:01:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:156;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:170;s:4:"type";s:14:"app_store_link";s:5:"value";s:69:"https://play.google.com/store/apps/details?id=com.cloudmart.cloudmart";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:11:29";s:10:"updated_at";s:19:"2025-01-31 23:01:26";}s:11:" * original";a:6:{s:2:"id";i:170;s:4:"type";s:14:"app_store_link";s:5:"value";s:69:"https://play.google.com/store/apps/details?id=com.cloudmart.cloudmart";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:11:29";s:10:"updated_at";s:19:"2025-01-31 23:01:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:157;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:171;s:4:"type";s:24:"product_approve_by_admin";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:26:08";s:10:"updated_at";s:19:"2023-12-19 17:26:08";}s:11:" * original";a:6:{s:2:"id";i:171;s:4:"type";s:24:"product_approve_by_admin";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:26:08";s:10:"updated_at";s:19:"2023-12-19 17:26:08";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:158;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:172;s:4:"type";s:23:"product_manage_by_admin";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:26:13";s:10:"updated_at";s:19:"2024-12-15 18:36:29";}s:11:" * original";a:6:{s:2:"id";i:172;s:4:"type";s:23:"product_manage_by_admin";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:26:13";s:10:"updated_at";s:19:"2024-12-15 18:36:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:159;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:173;s:4:"type";s:24:"product_query_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:26:34";s:10:"updated_at";s:19:"2025-03-16 00:18:42";}s:11:" * original";a:6:{s:2:"id";i:173;s:4:"type";s:24:"product_query_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 17:26:34";s:10:"updated_at";s:19:"2025-03-16 00:18:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:160;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:174;s:4:"type";s:18:"todays_deal_banner";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:58:51";s:10:"updated_at";s:19:"2023-12-20 03:06:39";}s:11:" * original";a:6:{s:2:"id";i:174;s:4:"type";s:18:"todays_deal_banner";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:58:51";s:10:"updated_at";s:19:"2023-12-20 03:06:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:161;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:175;s:4:"type";s:24:"todays_deal_banner_small";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:58:51";s:10:"updated_at";s:19:"2023-12-19 21:07:20";}s:11:" * original";a:6:{s:2:"id";i:175;s:4:"type";s:24:"todays_deal_banner_small";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:58:51";s:10:"updated_at";s:19:"2023-12-19 21:07:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:162;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:176;s:4:"type";s:20:"todays_deal_bg_color";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 20:58:51";s:10:"updated_at";s:19:"2023-12-19 20:58:51";}s:11:" * original";a:6:{s:2:"id";i:176;s:4:"type";s:20:"todays_deal_bg_color";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 20:58:51";s:10:"updated_at";s:19:"2023-12-19 20:58:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:163;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:177;s:4:"type";s:19:"home_banner1_images";s:5:"value";s:7:"["345"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:04";s:10:"updated_at";s:19:"2025-01-29 16:35:16";}s:11:" * original";a:6:{s:2:"id";i:177;s:4:"type";s:19:"home_banner1_images";s:5:"value";s:7:"["345"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:04";s:10:"updated_at";s:19:"2025-01-29 16:35:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:164;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:178;s:4:"type";s:19:"home_banner2_images";s:5:"value";s:7:"["342"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:15";s:10:"updated_at";s:19:"2025-01-29 16:35:37";}s:11:" * original";a:6:{s:2:"id";i:178;s:4:"type";s:19:"home_banner2_images";s:5:"value";s:7:"["342"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:15";s:10:"updated_at";s:19:"2025-01-29 16:35:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:165;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:179;s:4:"type";s:19:"home_banner3_images";s:5:"value";s:7:"["238"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:29";s:10:"updated_at";s:19:"2024-11-29 17:23:16";}s:11:" * original";a:6:{s:2:"id";i:179;s:4:"type";s:19:"home_banner3_images";s:5:"value";s:7:"["238"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:29";s:10:"updated_at";s:19:"2024-11-29 17:23:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:166;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:180;s:4:"type";s:18:"home_banner3_links";s:5:"value";s:24:"["https:\/\/zestpik.in"]";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 20:59:29";s:10:"updated_at";s:19:"2024-11-15 17:46:26";}s:11:" * original";a:6:{s:2:"id";i:180;s:4:"type";s:18:"home_banner3_links";s:5:"value";s:24:"["https:\/\/zestpik.in"]";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 20:59:29";s:10:"updated_at";s:19:"2024-11-15 17:46:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:167;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:181;s:4:"type";s:23:"coupon_background_image";s:5:"value";s:2:"32";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:41:37";}s:11:" * original";a:6:{s:2:"id";i:181;s:4:"type";s:23:"coupon_background_image";s:5:"value";s:2:"32";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:41:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:168;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:182;s:4:"type";s:22:"cupon_background_color";s:5:"value";s:7:"#13c6c9";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:42:52";}s:11:" * original";a:6:{s:2:"id";i:182;s:4:"type";s:22:"cupon_background_color";s:5:"value";s:7:"#13c6c9";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:42:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:169;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:183;s:4:"type";s:11:"cupon_title";s:5:"value";s:15:"Coupons Section";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:43:27";}s:11:" * original";a:6:{s:2:"id";i:183;s:4:"type";s:11:"cupon_title";s:5:"value";s:15:"Coupons Section";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:43:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:170;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:184;s:4:"type";s:14:"cupon_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:43:27";}s:11:" * original";a:6:{s:2:"id";i:184;s:4:"type";s:14:"cupon_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 20:59:49";s:10:"updated_at";s:19:"2024-04-11 15:43:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:171;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:185;s:4:"type";s:23:"classified_banner_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 21:00:22";s:10:"updated_at";s:19:"2023-12-20 03:01:14";}s:11:" * original";a:6:{s:2:"id";i:185;s:4:"type";s:23:"classified_banner_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 21:00:22";s:10:"updated_at";s:19:"2023-12-20 03:01:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:172;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:186;s:4:"type";s:29:"classified_banner_image_small";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 21:00:22";s:10:"updated_at";s:19:"2023-12-20 03:01:14";}s:11:" * original";a:6:{s:2:"id";i:186;s:4:"type";s:29:"classified_banner_image_small";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-19 21:00:22";s:10:"updated_at";s:19:"2023-12-20 03:01:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:173;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:187;s:4:"type";s:10:"top_brands";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 21:00:31";s:10:"updated_at";s:19:"2024-11-15 17:46:35";}s:11:" * original";a:6:{s:2:"id";i:187;s:4:"type";s:10:"top_brands";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-19 21:00:31";s:10:"updated_at";s:19:"2024-11-15 17:46:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:174;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:188;s:4:"type";s:26:"minimum_order_amount_check";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-01-17 01:23:41";s:10:"updated_at";s:19:"2024-01-17 01:23:41";}s:11:" * original";a:6:{s:2:"id";i:188;s:4:"type";s:26:"minimum_order_amount_check";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-01-17 01:23:41";s:10:"updated_at";s:19:"2024-01-17 01:23:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:175;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:189;s:4:"type";s:18:"show_website_popup";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-04-08 00:47:15";s:10:"updated_at";s:19:"2024-10-30 17:20:59";}s:11:" * original";a:6:{s:2:"id";i:189;s:4:"type";s:18:"show_website_popup";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-04-08 00:47:15";s:10:"updated_at";s:19:"2024-10-30 17:20:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:176;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:190;s:4:"type";s:21:"website_popup_content";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-04-08 00:47:15";s:10:"updated_at";s:19:"2024-04-08 00:47:15";}s:11:" * original";a:6:{s:2:"id";i:190;s:4:"type";s:21:"website_popup_content";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-04-08 00:47:15";s:10:"updated_at";s:19:"2024-04-08 00:47:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:177;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:191;s:4:"type";s:19:"show_subscribe_form";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2024-04-08 00:47:15";s:10:"updated_at";s:19:"2024-11-22 20:28:42";}s:11:" * original";a:6:{s:2:"id";i:191;s:4:"type";s:19:"show_subscribe_form";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2024-04-08 00:47:15";s:10:"updated_at";s:19:"2024-11-22 20:28:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:178;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:192;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";s:266:"<span style="color: rgb(133, 135, 150); font-family: Arial, sans-serif; font-size: 12.8px; text-align: center;">Copyright © Cloudmart </span><span style="color: rgb(133, 135, 150); font-family: Arial, sans-serif; font-size: 12.8px; text-align: center;">2024</span>";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-04-20 16:15:41";s:10:"updated_at";s:19:"2025-02-03 18:48:07";}s:11:" * original";a:6:{s:2:"id";i:192;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";s:266:"<span style="color: rgb(133, 135, 150); font-family: Arial, sans-serif; font-size: 12.8px; text-align: center;">Copyright © Cloudmart </span><span style="color: rgb(133, 135, 150); font-family: Arial, sans-serif; font-size: 12.8px; text-align: center;">2024</span>";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-04-20 16:15:41";s:10:"updated_at";s:19:"2025-02-03 18:48:07";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:179;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:193;s:4:"type";s:16:"tel_phone_number";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2024-10-30 16:07:28";s:10:"updated_at";s:19:"2025-02-12 18:31:02";}s:11:" * original";a:6:{s:2:"id";i:193;s:4:"type";s:16:"tel_phone_number";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2024-10-30 16:07:28";s:10:"updated_at";s:19:"2025-02-12 18:31:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:180;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:194;s:4:"type";s:21:"whatsapp_phone_number";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2024-10-30 16:07:28";s:10:"updated_at";s:19:"2025-02-12 18:31:02";}s:11:" * original";a:6:{s:2:"id";i:194;s:4:"type";s:21:"whatsapp_phone_number";s:5:"value";s:13:"+919606861856";s:4:"lang";N;s:10:"created_at";s:19:"2024-10-30 16:07:28";s:10:"updated_at";s:19:"2025-02-12 18:31:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:181;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:195;s:4:"type";s:28:"vendor_commission_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-10-30 22:54:43";s:10:"updated_at";s:19:"2024-11-22 20:20:31";}s:11:" * original";a:6:{s:2:"id";i:195;s:4:"type";s:28:"vendor_commission_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-10-30 22:54:43";s:10:"updated_at";s:19:"2024-11-22 20:20:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:182;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:196;s:4:"type";s:12:"footer_title";s:5:"value";s:14:"cloud-mart.com";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-11-04 15:21:27";s:10:"updated_at";s:19:"2025-01-31 22:59:27";}s:11:" * original";a:6:{s:2:"id";i:196;s:4:"type";s:12:"footer_title";s:5:"value";s:14:"cloud-mart.com";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-11-04 15:21:27";s:10:"updated_at";s:19:"2025-01-31 22:59:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:183;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:197;s:4:"type";s:18:"footer_description";s:5:"value";s:9:"Karnataka";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-11-04 15:21:27";s:10:"updated_at";s:19:"2025-01-31 22:59:35";}s:11:" * original";a:6:{s:2:"id";i:197;s:4:"type";s:18:"footer_description";s:5:"value";s:9:"Karnataka";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-11-04 15:21:27";s:10:"updated_at";s:19:"2025-01-31 22:59:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:184;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:198;s:4:"type";s:15:"contact_address";s:5:"value";s:20:"Bangalore, Karnataka";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-11-04 15:25:40";s:10:"updated_at";s:19:"2025-01-31 23:02:27";}s:11:" * original";a:6:{s:2:"id";i:198;s:4:"type";s:15:"contact_address";s:5:"value";s:20:"Bangalore, Karnataka";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-11-04 15:25:40";s:10:"updated_at";s:19:"2025-01-31 23:02:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:185;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:199;s:4:"type";s:13:"header_script";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-11-13 22:42:29";s:10:"updated_at";s:19:"2025-01-22 22:07:41";}s:11:" * original";a:6:{s:2:"id";i:199;s:4:"type";s:13:"header_script";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-11-13 22:42:29";s:10:"updated_at";s:19:"2025-01-22 22:07:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:186;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:200;s:4:"type";s:13:"footer_script";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-11-13 22:42:29";s:10:"updated_at";s:19:"2024-11-13 22:42:29";}s:11:" * original";a:6:{s:2:"id";i:200;s:4:"type";s:13:"footer_script";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-11-13 22:42:29";s:10:"updated_at";s:19:"2024-11-13 22:42:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:187;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:201;s:4:"type";s:27:"display_terms_pp_quicklinks";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-14 04:12:57";s:10:"updated_at";s:19:"2024-12-03 18:50:56";}s:11:" * original";a:6:{s:2:"id";i:201;s:4:"type";s:27:"display_terms_pp_quicklinks";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-14 04:12:57";s:10:"updated_at";s:19:"2024-12-03 18:50:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:188;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:202;s:4:"type";s:27:"enable_search_with_location";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-14 04:26:24";s:10:"updated_at";s:19:"2025-03-16 00:17:51";}s:11:" * original";a:6:{s:2:"id";i:202;s:4:"type";s:27:"enable_search_with_location";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-14 04:26:24";s:10:"updated_at";s:19:"2025-03-16 00:17:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:189;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:203;s:4:"type";s:15:"seller_app_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-11-18 16:49:09";s:10:"updated_at";s:19:"2024-11-18 16:49:09";}s:11:" * original";a:6:{s:2:"id";i:203;s:4:"type";s:15:"seller_app_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-11-18 16:49:09";s:10:"updated_at";s:19:"2024-11-18 16:49:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:190;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:204;s:4:"type";s:29:"guest_can_add_product_to_cart";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-19 19:30:48";s:10:"updated_at";s:19:"2024-11-19 19:30:48";}s:11:" * original";a:6:{s:2:"id";i:204;s:4:"type";s:29:"guest_can_add_product_to_cart";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-19 19:30:48";s:10:"updated_at";s:19:"2024-11-19 19:30:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:191;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:205;s:4:"type";s:30:"minimum_seller_amount_withdraw";s:5:"value";s:3:"100";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-22 20:20:23";s:10:"updated_at";s:19:"2024-11-22 20:20:23";}s:11:" * original";a:6:{s:2:"id";i:205;s:4:"type";s:30:"minimum_seller_amount_withdraw";s:5:"value";s:3:"100";s:4:"lang";N;s:10:"created_at";s:19:"2024-11-22 20:20:23";s:10:"updated_at";s:19:"2024-11-22 20:20:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:192;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:206;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";s:2:"in";s:10:"created_at";s:19:"2024-11-22 20:26:53";s:10:"updated_at";s:19:"2024-11-22 20:26:53";}s:11:" * original";a:6:{s:2:"id";i:206;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";s:2:"in";s:10:"created_at";s:19:"2024-11-22 20:26:53";s:10:"updated_at";s:19:"2024-11-22 20:26:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:193;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:207;s:4:"type";s:23:"color_filter_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-12-02 23:04:10";s:10:"updated_at";s:19:"2025-03-03 00:39:02";}s:11:" * original";a:6:{s:2:"id";i:207;s:4:"type";s:23:"color_filter_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-12-02 23:04:10";s:10:"updated_at";s:19:"2025-03-03 00:39:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:194;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:208;s:4:"type";s:20:"enable_rental_module";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-12-04 13:31:00";s:10:"updated_at";s:19:"2025-01-22 20:15:30";}s:11:" * original";a:6:{s:2:"id";i:208;s:4:"type";s:20:"enable_rental_module";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-12-04 13:31:00";s:10:"updated_at";s:19:"2025-01-22 20:15:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}