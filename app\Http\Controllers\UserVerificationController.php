<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\UserVerificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserVerificationController extends Controller
{
    protected $verificationService;

    public function __construct()
    {
        $this->verificationService = new UserVerificationService();
    }

    /**
     * Show verification dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $status = $this->verificationService->getVerificationStatus($user);
        
        return view('frontend.user.verification', compact('user', 'status'));
    }

    /**
     * Send phone verification OTP
     */
    public function sendPhoneOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'country_code' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid phone number format',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        // Update phone number if provided
        if ($request->phone !== $user->phone || $request->country_code !== $user->country_code) {
            // Check if phone number is already taken
            $existingUser = User::where('phone', $request->phone)
                               ->where('country_code', $request->country_code)
                               ->where('id', '!=', $user->id)
                               ->first();
            
            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'This phone number is already registered with another account'
                ], 422);
            }

            $user->update([
                'phone' => $request->phone,
                'country_code' => $request->country_code,
                'is_phone_verified' => false,
                'phone_verified_at' => null
            ]);
        }

        $result = $this->verificationService->sendPhoneVerification($user);
        
        return response()->json($result);
    }

    /**
     * Send email verification OTP
     */
    public function sendEmailOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email format',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        // Update email if provided
        if ($request->email !== $user->email) {
            // Check if email is already taken
            $existingUser = User::where('email', $request->email)
                               ->where('id', '!=', $user->id)
                               ->first();
            
            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'This email is already registered with another account'
                ], 422);
            }

            $user->update([
                'email' => $request->email,
                'is_email_verified' => false,
                'email_verified_at' => null
            ]);
        }

        $result = $this->verificationService->sendEmailVerification($user);
        
        return response()->json($result);
    }

    /**
     * Verify phone OTP
     */
    public function verifyPhoneOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required|string|size:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid OTP format',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $result = $this->verificationService->verifyPhoneOtp($user, $request->otp);
        
        return response()->json($result);
    }

    /**
     * Verify email OTP
     */
    public function verifyEmailOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required|string|size:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid OTP format',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $result = $this->verificationService->verifyEmailOtp($user, $request->otp);
        
        return response()->json($result);
    }

    /**
     * Get verification status
     */
    public function getStatus()
    {
        $user = Auth::user();
        $status = $this->verificationService->getVerificationStatus($user);
        
        return response()->json([
            'success' => true,
            'status' => $status
        ]);
    }

    /**
     * Resend verification for both email and phone
     */
    public function resendVerification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:phone,email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid verification type',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        if ($request->type === 'phone') {
            if (!$user->phone) {
                return response()->json([
                    'success' => false,
                    'message' => 'No phone number found. Please add a phone number first.'
                ], 422);
            }
            
            $result = $this->verificationService->sendPhoneVerification($user);
        } else {
            if (!$user->email) {
                return response()->json([
                    'success' => false,
                    'message' => 'No email address found. Please add an email address first.'
                ], 422);
            }
            
            $result = $this->verificationService->sendEmailVerification($user);
        }
        
        return response()->json($result);
    }

    /**
     * Check if user is verified (middleware helper)
     */
    public function checkVerificationStatus(User $user = null)
    {
        $user = $user ?: Auth::user();
        
        if (!$user) {
            return false;
        }

        // For sellers, require both email and phone verification
        if ($user->user_type === 'seller') {
            return $user->hasVerifiedEmail() && $user->hasVerifiedPhone();
        }

        // For customers, require at least one verification method
        return $user->hasVerifiedEmail() || $user->hasVerifiedPhone();
    }

    /**
     * Force verification redirect
     */
    public function requireVerification()
    {
        $user = Auth::user();
        $status = $this->verificationService->getVerificationStatus($user);
        
        if ($user->user_type === 'seller') {
            return redirect()->route('seller.verification')->with('verification_required', true);
        }
        
        return redirect()->route('user.verification')->with('verification_required', true);
    }
}
